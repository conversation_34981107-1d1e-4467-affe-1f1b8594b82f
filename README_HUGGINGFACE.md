---
title: Gaming News Agent
emoji: 🎮
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---

# 🎮 وكيل أخبار الألعاب - Gaming News Agent

## 🌟 نظرة عامة

وكيل ذكي متطور لجمع ونشر أخبار الألعاب تلقائياً باستخدام أحدث تقنيات الذكاء الاصطناعي. يدعم توليد الصور المتقدم ونشر المحتوى على منصات متعددة.

## ✨ المميزات الرئيسية

### 🤖 ذكاء اصطناعي متقدم
- **Gemini 2.5 Pro** للتحليل والكتابة
- **4 خدمات توليد صور** (OpenArt, Leap AI, DeepAI, Replicate)
- **نظام احتياطي ذكي** مع 7 خدمات إجمالية

### 📰 إدارة المحتوى
- جمع الأخبار من مصادر متعددة
- تحليل وتصنيف تلقائي
- توليد صور مخصصة لكل مقال
- نشر تلقائي على Telegram و Blogger

### 🎯 واجهة إدارية شاملة
- **لوحة معلومات** في الوقت الفعلي
- **إدارة APIs** مع إمكانية التعديل
- **Terminal مباشر** لمراقبة السجلات
- **إحصائيات مفصلة** للأداء

## 🚀 كيفية الاستخدام

### 1. إعداد مفاتيح API

في تبويب "🔑 إدارة APIs"، أضف مفاتيحك:

#### APIs الأساسية:
- **Gemini API Key**: للذكاء الاصطناعي
- **Telegram Bot Token**: للنشر على Telegram
- **Blogger Client ID**: للنشر على Blogger
- **Google Search API Key**: للبحث

#### APIs توليد الصور:
- **OpenArt API Key**: جودة عالية
- **Leap AI API Key**: سرعة عالية  
- **DeepAI API Key**: مجاني
- **Replicate API Key**: نماذج متنوعة

### 2. تشغيل الوكيل

1. انتقل لتبويب "📊 لوحة المعلومات"
2. اضغط "▶️ تشغيل الوكيل"
3. راقب الحالة في الوقت الفعلي

### 3. مراقبة الأداء

- **Terminal**: لمراقبة السجلات والأخطاء
- **الإحصائيات**: لمتابعة المقالات المنشورة
- **اختبار APIs**: للتأكد من عمل الخدمات

## 🔧 الحصول على مفاتيح API

### 🤖 Gemini API
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. انسخ المفتاح

### 🎨 OpenArt
1. اذهب إلى [OpenArt](https://openart.ai)
2. انتقل إلى [API Settings](https://openart.ai/account/api)
3. اضغط "Generate API Key"

### 🚀 Leap AI
1. اذهب إلى [Leap AI](https://tryleap.ai)
2. انتقل إلى [API Keys](https://tryleap.ai/account/api-keys)
3. اضغط "Create API Key"

### 🧠 DeepAI
1. اذهب إلى [DeepAI](https://deepai.org)
2. انتقل إلى [Dashboard](https://deepai.org/dashboard)
3. احصل على مفتاح API

### 🔄 Replicate
1. اذهب إلى [Replicate](https://replicate.com)
2. انتقل إلى [API Tokens](https://replicate.com/account/api-tokens)
3. اضغط "Generate token"

### 📱 Telegram Bot
1. ابحث عن [@BotFather](https://t.me/botfather) في Telegram
2. أرسل `/newbot`
3. اتبع التعليمات واحصل على Token

### 📝 Blogger
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد
3. فعل Blogger API
4. أنشئ OAuth 2.0 credentials

## 📊 الإحصائيات والمراقبة

### لوحة المعلومات
- حالة النظام في الوقت الفعلي
- إحصائيات المقالات
- حالة APIs
- معلومات قاعدة البيانات

### Terminal
- سجلات مفصلة للعمليات
- رسائل الأخطاء والتحذيرات
- تحديث تلقائي كل 5 ثواني
- إمكانية تخصيص عدد الأسطر

### اختبار APIs
- اختبار تلقائي لجميع خدمات الصور
- تقارير مفصلة للأداء
- توصيات للتحسين

## 🛡️ الأمان والموثوقية

### نظام احتياطي متعدد المستويات
1. **المستوى الأول**: APIs الجديدة (OpenArt, Leap AI, DeepAI, Replicate)
2. **المستوى الثاني**: APIs الاحتياطية (Pollinations, Freepik, FluxAI)
3. **المستوى الثالث**: صور Placeholder

### حماية البيانات
- تشفير مفاتيح API
- تخزين آمن للإعدادات
- نسخ احتياطية تلقائية

## 🎯 نصائح للاستخدام الأمثل

### للحصول على أفضل النتائج:
1. **أضف مفاتيح APIs متعددة** لضمان التوفر
2. **راقب الإحصائيات** لتحسين الأداء
3. **استخدم Terminal** لمتابعة العمليات
4. **اختبر APIs دورياً** للتأكد من العمل

### كتابة Prompts فعالة للصور:
```
# جيد
"gaming controller"

# أفضل
"Professional gaming controller, RGB lighting, futuristic design, high quality"
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

#### "API key not provided"
- تأكد من إضافة المفتاح في تبويب إدارة APIs
- اضغط "💾 حفظ جميع المفاتيح"

#### "API call failed"
- فحص اتصال الإنترنت
- التأكد من صحة المفتاح
- استخدام "🧪 اختبار APIs الصور"

#### "Bot not starting"
- تأكد من وجود جميع المفاتيح الأساسية
- راجع Terminal للأخطاء المفصلة

## 📞 الدعم والمساعدة

### للمساعدة:
1. راجع Terminal للأخطاء المفصلة
2. استخدم "🧪 اختبار APIs الصور"
3. تأكد من صحة جميع المفاتيح
4. راجع الإحصائيات للتأكد من الأداء

## 🎉 الخلاصة

وكيل أخبار الألعاب يوفر:
- ✅ **ذكاء اصطناعي متقدم** مع Gemini 2.5 Pro
- ✅ **4 خدمات توليد صور** حديثة
- ✅ **واجهة إدارية شاملة** مع Terminal
- ✅ **نظام احتياطي ذكي** للموثوقية
- ✅ **مراقبة شاملة** للأداء
- ✅ **سهولة الاستخدام** على Hugging Face

**ابدأ الآن واستمتع بإنشاء محتوى احترافي تلقائياً! 🚀**

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى فتح issue أو pull request للتحسينات.
