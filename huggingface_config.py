# -*- coding: utf-8 -*-
"""
تكوين خاص بـ Hugging Face Spaces
"""

import os
import sys
from pathlib import Path

class HuggingFaceConfig:
    """إعدادات خاصة بـ Hugging Face"""
    
    def __init__(self):
        # مسارات الملفات
        self.base_dir = Path(__file__).parent
        self.logs_dir = self.base_dir / "logs"
        self.cache_dir = self.base_dir / "cache"
        self.data_dir = self.base_dir / "data"
        
        # إنشاء المجلدات المطلوبة
        self.create_directories()
        
        # إعدادات Gradio
        self.gradio_config = {
            'server_name': '0.0.0.0',
            'server_port': 7860,
            'share': False,  # False في Hugging Face
            'show_error': True,
            'debug': False,  # False في الإنتاج
            'enable_queue': True,
            'max_threads': 10
        }
        
        # إعدادات الأمان
        self.security_config = {
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'allowed_file_types': ['.txt', '.json', '.log'],
            'rate_limit': 100,  # طلبات في الدقيقة
            'session_timeout': 3600  # ساعة واحدة
        }
        
        # إعدادات الأداء
        self.performance_config = {
            'max_log_lines': 1000,
            'cache_size': 100,
            'auto_cleanup': True,
            'cleanup_interval': 3600  # ساعة واحدة
        }
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            self.logs_dir,
            self.cache_dir,
            self.data_dir,
            self.cache_dir / "images",
            self.cache_dir / "manual_images"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def setup_environment(self):
        """إعداد متغيرات البيئة"""
        # إعدادات افتراضية للبيئة
        default_env = {
            'GRADIO_SERVER_NAME': '0.0.0.0',
            'GRADIO_SERVER_PORT': '7860',
            'PYTHONPATH': str(self.base_dir),
            'TOKENIZERS_PARALLELISM': 'false',  # تجنب تحذيرات tokenizers
            'HF_HUB_DISABLE_SYMLINKS_WARNING': '1'
        }
        
        for key, value in default_env.items():
            if key not in os.environ:
                os.environ[key] = value
    
    def get_demo_credentials(self):
        """الحصول على بيانات تجريبية للعرض"""
        return {
            'gemini_key': 'demo_gemini_key_here',
            'telegram_token': 'demo_telegram_token_here',
            'openart_key': 'demo_openart_key_here',
            'leap_ai_key': 'demo_leap_ai_key_here',
            'deepai_key': 'demo_deepai_key_here',
            'replicate_key': 'demo_replicate_key_here'
        }
    
    def is_huggingface_space(self):
        """فحص ما إذا كان التطبيق يعمل على Hugging Face"""
        return 'SPACE_ID' in os.environ or 'HF_SPACE' in os.environ
    
    def get_space_info(self):
        """الحصول على معلومات Space"""
        if self.is_huggingface_space():
            return {
                'space_id': os.environ.get('SPACE_ID', 'unknown'),
                'space_author': os.environ.get('SPACE_AUTHOR', 'unknown'),
                'space_title': os.environ.get('SPACE_TITLE', 'Gaming News Agent'),
                'space_sdk': os.environ.get('SPACE_SDK', 'gradio'),
                'space_sdk_version': os.environ.get('SPACE_SDK_VERSION', '4.0.0')
            }
        return None
    
    def setup_logging(self):
        """إعداد نظام التسجيل للـ Hugging Face"""
        import logging
        
        # إنشاء ملف السجل
        log_file = self.logs_dir / "bot.log"
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger('gaming_news_agent')
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية للعرض"""
        import sqlite3
        import json
        from datetime import datetime
        
        # إنشاء قاعدة بيانات تجريبية
        db_file = self.data_dir / "articles.db"

        # إنشاء قاعدة البيانات دائماً (حتى لو كانت موجودة)
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()

        # إنشاء جدول المقالات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                published INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                source TEXT,
                category TEXT
            )
        ''')

        # فحص ما إذا كانت البيانات موجودة
        cursor.execute("SELECT COUNT(*) FROM articles")
        count = cursor.fetchone()[0]

        # إضافة بيانات تجريبية إذا لم تكن موجودة
        if count == 0:
            sample_articles = [
                ("مراجعة لعبة Cyberpunk 2077 الجديدة", "محتوى المراجعة...", 1, datetime.now().isoformat(), "GameSpot", "review"),
                ("أخبار PlayStation 5 الجديدة", "آخر أخبار PlayStation...", 1, datetime.now().isoformat(), "IGN", "news"),
                ("دليل لعبة Elden Ring", "دليل شامل للعبة...", 0, datetime.now().isoformat(), "GameInformer", "guide"),
                ("إعلان لعبة جديدة من Nintendo", "Nintendo تعلن عن...", 0, datetime.now().isoformat(), "Nintendo", "news"),
                ("مراجعة جهاز Steam Deck", "مراجعة مفصلة لجهاز...", 1, datetime.now().isoformat(), "TechRadar", "review")
            ]

            cursor.executemany(
                "INSERT INTO articles (title, content, published, created_at, source, category) VALUES (?, ?, ?, ?, ?, ?)",
                sample_articles
            )

        conn.commit()
        conn.close()
        
        # إنشاء ملف سجل تجريبي
        log_file = self.logs_dir / "bot.log"
        if not log_file.exists():
            sample_logs = [
                f"{datetime.now()} - gaming_news_agent - INFO - تم بدء تشغيل الوكيل",
                f"{datetime.now()} - gaming_news_agent - INFO - تم تحميل التكوين بنجاح",
                f"{datetime.now()} - gaming_news_agent - INFO - تم الاتصال بقاعدة البيانات",
                f"{datetime.now()} - gaming_news_agent - INFO - تم تفعيل APIs توليد الصور",
                f"{datetime.now()} - gaming_news_agent - INFO - الوكيل جاهز للعمل",
                f"{datetime.now()} - gaming_news_agent - INFO - تم إنشاء مقال جديد: مراجعة لعبة جديدة",
                f"{datetime.now()} - gaming_news_agent - INFO - تم توليد صورة باستخدام OpenArt API",
                f"{datetime.now()} - gaming_news_agent - INFO - تم نشر المقال على Telegram",
                f"{datetime.now()} - gaming_news_agent - WARNING - معدل استخدام API مرتفع",
                f"{datetime.now()} - gaming_news_agent - INFO - تم حفظ النسخة الاحتياطية"
            ]
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(sample_logs))

# إنشاء كائن التكوين العام
hf_config = HuggingFaceConfig()
