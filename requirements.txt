# متطلبات وكيل أخبار الألعاب - محسن للعمل على Hugging Face Spaces
# الذكاء الاصطناعي والمحتوى
google-generativeai>=0.8.0
google-api-python-client>=2.110.0
google-auth-oauthlib>=1.1.0
google-auth>=2.23.0

# واجهة Gradio للـ Hugging Face
gradio>=4.0.0

# تيليجرام
python-telegram-bot==20.6
aiohttp>=3.9.0

# استخراج المحتوى ومعالجة الويب
beautifulsoup4>=4.12.0
requests>=2.31.0
lxml>=4.9.0
html5lib>=1.1

# معالجة النصوص والتحليل
nltk>=3.8.0
textblob>=0.17.0
python-dateutil>=2.8.0

# YouTube transcript extraction
youtube-transcript-api>=1.6.0

# الصور والوسائط
Pillow>=10.1.0
imageio>=2.31.0

# الأدوات المساعدة
aiofiles>=23.2.0
python-dotenv>=1.0.0
schedule>=1.2.0
pytz>=2023.3

# JSON والبيانات
ujson>=5.8.0
pydantic>=2.4.0

# التسجيل المتقدم
colorlog>=6.7.0

# أدوات الشبكة
httpx>=0.25.0

# معالجة الوقت والتاريخ
arrow>=1.3.0

# أدوات التحليل الأساسية
pandas>=2.1.0
numpy>=1.25.0

# أدوات التحكم
click>=8.1.0

# إنشاء الصور بالذكاء الاصطناعي
opencv-python-headless>=4.8.0
matplotlib>=3.7.0

# أدوات النظام الأساسية
psutil>=5.9.0
